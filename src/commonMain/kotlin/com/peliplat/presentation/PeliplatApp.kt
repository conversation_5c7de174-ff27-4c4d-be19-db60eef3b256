package com.peliplat.presentation

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.peliplat.data.remote.ApiInitializer
import com.peliplat.domain.usecase.*
import com.peliplat.presentation.screen.*
import com.peliplat.presentation.theme.PeliplatTheme
import com.peliplat.presentation.viewmodel.ArticleListViewModel
import com.peliplat.presentation.viewmodel.AuthViewModel

@Composable
fun PeliplatApp() {
    PeliplatTheme {
        val navController = rememberNavController()
        var isApiInitialized by remember { mutableStateOf(false) }

        // Initialize API integration - Production only
        LaunchedEffect(Unit) {
            try {
                if (!ApiInitializer.isInitialized()) {
                    // Connect to production Peliplat API only
                    ApiInitializer.initialize(useProductionApi = true)
                    println("🌐 Connected to production Peliplat API")

                    // Test the API integration
                    ApiInitializer.testApiIntegration()
                }
                isApiInitialized = true
            } catch (e: Exception) {
                println("❌ Failed to initialize production API: ${e.message}")
                println("⚠️ App will show error states instead of fallback data")
                // Don't use fallback - better to show error than wrong data
                isApiInitialized = true
            }
        }

        if (!isApiInitialized) {
            // Show loading screen while API initializes
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator()
                    Spacer(modifier = Modifier.height(16.dp))
                    Text("Initializing Peliplat...")
                }
            }
            return@PeliplatTheme
        }

        // Get repositories from initializer (now safe to access)
        val authRepository = remember { ApiInitializer.provideAuthRepository() }
        val articleRepository = remember { ApiInitializer.provideArticleRepository() }
        
        // Initialize use cases
        val loginUseCase = remember { LoginUseCase(authRepository) }
        val registerUseCase = remember { RegisterUseCase(authRepository) }
        val logoutUseCase = remember { LogoutUseCase(authRepository) }
        val getCurrentUserUseCase = remember { GetCurrentUserUseCase(authRepository) }
        val getArticlesUseCase = remember { GetArticlesUseCase(articleRepository) }
        val likeArticleUseCase = remember { LikeArticleUseCase(articleRepository) }
        val bookmarkArticleUseCase = remember { BookmarkArticleUseCase(articleRepository) }
        
        // Initialize ViewModels
        val authViewModel = remember {
            AuthViewModel(loginUseCase, registerUseCase, logoutUseCase, getCurrentUserUseCase)
        }
        val articleListViewModel = remember {
            ArticleListViewModel(getArticlesUseCase, likeArticleUseCase, bookmarkArticleUseCase)
        }
        
        // Navigation
        NavHost(
            navController = navController,
            startDestination = if (authViewModel.uiState.isLoggedIn) "main" else "login"
        ) {
            composable("login") {
                LoginScreen(
                    authViewModel = authViewModel,
                    onNavigateToRegister = { navController.navigate("register") },
                    onLoginSuccess = { 
                        navController.navigate("main") {
                            popUpTo("login") { inclusive = true }
                        }
                    }
                )
            }
            
            composable("register") {
                RegisterScreen(
                    authViewModel = authViewModel,
                    onNavigateToLogin = { navController.popBackStack() },
                    onRegisterSuccess = { 
                        navController.navigate("main") {
                            popUpTo("login") { inclusive = true }
                        }
                    }
                )
            }
            
            composable("main") {
                MainScreen(
                    authViewModel = authViewModel,
                    articleListViewModel = articleListViewModel,
                    onLogout = {
                        navController.navigate("login") {
                            popUpTo("main") { inclusive = true }
                        }
                    }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    authViewModel: AuthViewModel,
    articleListViewModel: ArticleListViewModel,
    onLogout: () -> Unit,
    modifier: Modifier = Modifier
) {
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = when (currentRoute) {
                            "home" -> "Peliplat"
                            "articles" -> "Articles"
                            "discussions" -> "Discussions"
                            "library" -> "Library"
                            "challenge" -> "Challenge"
                            else -> "Peliplat"
                        }
                    )
                }
            )
        },
        bottomBar = {
            NavigationBar {
                NavigationBarItem(
                    icon = { Text("🏠") },
                    label = { Text("Home") },
                    selected = currentRoute == "home",
                    onClick = {
                        navController.navigate("home") {
                            popUpTo("home") { inclusive = true }
                        }
                    }
                )
                NavigationBarItem(
                    icon = { Text("📰") },
                    label = { Text("Articles") },
                    selected = currentRoute == "articles",
                    onClick = {
                        navController.navigate("articles") {
                            popUpTo("home")
                        }
                    }
                )
                NavigationBarItem(
                    icon = { Text("💬") },
                    label = { Text("Discussions") },
                    selected = currentRoute == "discussions",
                    onClick = {
                        navController.navigate("discussions") {
                            popUpTo("home")
                        }
                    }
                )
                NavigationBarItem(
                    icon = { Text("📚") },
                    label = { Text("Library") },
                    selected = currentRoute == "library",
                    onClick = {
                        navController.navigate("library") {
                            popUpTo("home")
                        }
                    }
                )
                NavigationBarItem(
                    icon = { Text("🏆") },
                    label = { Text("Challenge") },
                    selected = currentRoute == "challenge",
                    onClick = {
                        navController.navigate("challenge") {
                            popUpTo("home")
                        }
                    }
                )
            }
        }
    ) { paddingValues ->
        NavHost(
            navController = navController,
            startDestination = "home",
            modifier = Modifier.padding(paddingValues)
        ) {
            composable("home") {
                HomeScreen(
                    articleListViewModel = articleListViewModel,
                    onArticleClick = { articleId ->
                        // Navigate to article detail
                    },
                    onAuthorClick = { authorId ->
                        // Navigate to author profile
                    }
                )
            }

            composable("articles") {
                ArticlesScreen(
                    articleListViewModel = articleListViewModel,
                    onArticleClick = { articleId ->
                        // Navigate to article detail
                    },
                    onAuthorClick = { authorId ->
                        // Navigate to author profile
                    }
                )
            }

            composable("discussions") {
                DiscussionsScreen(
                    onDiscussionClick = { discussionId ->
                        // Navigate to discussion detail
                    },
                    onAuthorClick = { authorId ->
                        // Navigate to author profile
                    }
                )
            }

            composable("library") {
                LibraryScreen()
            }

            composable("challenge") {
                ChallengeScreen()
            }
        }
    }
}

