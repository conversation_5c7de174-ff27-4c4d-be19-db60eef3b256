package com.peliplat.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChallengeScreen(
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header
        item {
            Column {
                Text(
                    text = "Writing Challenges",
                    style = MaterialTheme.typography.headlineMedium
                )
                Text(
                    text = "Join writing challenges and improve your movie criticism skills",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        // Active challenges
        item {
            Text(
                text = "Active Challenges",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(vertical = 8.dp)
            )
        }
        
        items(activeChallenges) { challenge ->
            ChallengeCard(
                challenge = challenge,
                onClick = { /* Navigate to challenge detail */ }
            )
        }
        
        // Upcoming challenges
        item {
            Text(
                text = "Upcoming Challenges",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(vertical = 8.dp)
            )
        }
        
        items(upcomingChallenges) { challenge ->
            ChallengeCard(
                challenge = challenge,
                onClick = { /* Navigate to challenge detail */ }
            )
        }
        
        // Challenge info
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(20.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "🏆",
                        style = MaterialTheme.typography.displayMedium
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "Join the Community",
                        style = MaterialTheme.typography.titleLarge,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Participate in writing challenges to improve your skills, connect with other movie enthusiasts, and win exciting prizes!",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChallengeCard(
    challenge: Challenge,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        onClick = onClick
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = challenge.title,
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Text(
                        text = challenge.description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Surface(
                    shape = RoundedCornerShape(12.dp),
                    color = when (challenge.status) {
                        ChallengeStatus.ACTIVE -> Color(0xFF4CAF50)
                        ChallengeStatus.UPCOMING -> Color(0xFFFF9800)
                        ChallengeStatus.COMPLETED -> Color(0xFF9E9E9E)
                    }
                ) {
                    Text(
                        text = challenge.status.name,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp),
                        style = MaterialTheme.typography.labelSmall,
                        color = Color.White,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "📅 ${challenge.deadline}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                Text(
                    text = "👥 ${challenge.participants} participants",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

data class Challenge(
    val title: String,
    val description: String,
    val deadline: String,
    val participants: Int,
    val status: ChallengeStatus
)

enum class ChallengeStatus {
    ACTIVE, UPCOMING, COMPLETED
}

private val activeChallenges = listOf(
    Challenge(
        title = "Horror Movie Marathon Review",
        description = "Write a comprehensive review of your favorite horror movie from the past decade",
        deadline = "Dec 31, 2024",
        participants = 156,
        status = ChallengeStatus.ACTIVE
    ),
    Challenge(
        title = "Character Analysis Deep Dive",
        description = "Analyze a complex character from any movie or TV series",
        deadline = "Jan 15, 2025",
        participants = 89,
        status = ChallengeStatus.ACTIVE
    )
)

private val upcomingChallenges = listOf(
    Challenge(
        title = "Best of 2024 Roundup",
        description = "Share your top 10 movies of 2024 with detailed explanations",
        deadline = "Feb 1, 2025",
        participants = 0,
        status = ChallengeStatus.UPCOMING
    ),
    Challenge(
        title = "Underrated Gems",
        description = "Highlight an underrated movie that deserves more recognition",
        deadline = "Feb 15, 2025",
        participants = 0,
        status = ChallengeStatus.UPCOMING
    )
)
