package com.peliplat.presentation.screen

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.peliplat.presentation.components.ArticleCard
import com.peliplat.presentation.components.ErrorMessage
import com.peliplat.presentation.components.LoadingIndicator
import com.peliplat.presentation.viewmodel.ArticleListViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ArticlesScreen(
    articleListViewModel: ArticleListViewModel,
    onArticleClick: (String) -> Unit,
    onAuthorClick: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    val uiState = articleListViewModel.uiState
    val listState = rememberLazyListState()

    // Load more articles when reaching the end
    LaunchedEffect(listState) {
        snapshotFlow { listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index }
            .collect { lastVisibleIndex ->
                if (lastVisibleIndex != null &&
                    lastVisibleIndex >= uiState.articles.size - 3 &&
                    uiState.hasMore &&
                    !uiState.isLoading) {
                    articleListViewModel.loadMoreArticles()
                }
            }
    }
    
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        LazyColumn(
            state = listState,
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(vertical = 8.dp)
        ) {
            // Header
            item {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "All Articles",
                        style = MaterialTheme.typography.headlineMedium
                    )
                    Text(
                        text = "Browse all movie articles and reviews",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // Articles
            items(uiState.articles) { article ->
                ArticleCard(
                    article = article,
                    onArticleClick = onArticleClick,
                    onAuthorClick = onAuthorClick,
                    onLikeClick = { articleListViewModel.likeArticle(it) },
                    onBookmarkClick = { articleListViewModel.bookmarkArticle(it) }
                )
            }
            
            // Loading indicator for pagination
            if (uiState.isLoading && uiState.articles.isNotEmpty()) {
                item {
                    LoadingIndicator(
                        modifier = Modifier.padding(16.dp)
                    )
                }
            }
            
            // Error message
            uiState.error?.let { error ->
                item {
                    ErrorMessage(
                        message = error,
                        onRetry = { articleListViewModel.clearError() },
                        modifier = Modifier.padding(16.dp)
                    )
                }
            }
        }
        
        // Initial loading state
        if (uiState.isLoading && uiState.articles.isEmpty()) {
            LoadingIndicator(
                modifier = Modifier.align(Alignment.Center)
            )
        }
        
        // Empty state
        if (!uiState.isLoading && uiState.articles.isEmpty() && uiState.error == null) {
            Column(
                modifier = Modifier
                    .align(Alignment.Center)
                    .padding(32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "📰",
                    style = MaterialTheme.typography.displayMedium
                )
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = "No articles yet",
                    style = MaterialTheme.typography.titleLarge
                )
                Text(
                    text = "Check back later for new content!",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}
