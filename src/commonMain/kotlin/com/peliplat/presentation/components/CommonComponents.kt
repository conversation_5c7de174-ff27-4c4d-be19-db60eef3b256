package com.peliplat.presentation.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.peliplat.domain.model.Article
import com.peliplat.domain.model.User

@OptIn(ExperimentalMaterial3Api::class)

@Composable
fun ArticleCard(
    article: Article,
    onArticleClick: (String) -> Unit,
    onAuthorClick: (String) -> Unit,
    onLikeClick: (String) -> Unit,
    onBookmarkClick: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 6.dp),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        onClick = { onArticleClick(article.id) }
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Author info
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                UserAvatar(
                    user = article.author,
                    size = 40.dp,
                    onClick = { onAuthorClick(article.author.id) }
                )
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = article.author.displayName,
                        style = MaterialTheme.typography.titleSmall,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = formatTimeAgo(article.publishedAt),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Article title
            Text(
                text = article.title,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            // Article excerpt
            Text(
                text = article.excerpt,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 3,
                overflow = TextOverflow.Ellipsis
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Tags
            if (article.tags.isNotEmpty()) {
                TagRow(tags = article.tags.take(3))
                Spacer(modifier = Modifier.height(12.dp))
            }
            
            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row {
                    ActionButton(
                        icon = if (article.isLiked) "❤️" else "🤍",
                        text = article.likesCount.toString(),
                        onClick = { onLikeClick(article.id) }
                    )
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    ActionButton(
                        icon = "💬",
                        text = article.commentsCount.toString(),
                        onClick = { onArticleClick(article.id) }
                    )
                }
                
                IconButton(
                    onClick = { onBookmarkClick(article.id) }
                ) {
                    Text(
                        text = if (article.isBookmarked) "🔖" else "📑",
                        style = MaterialTheme.typography.titleMedium
                    )
                }
            }
        }
    }
}

@Composable
fun UserAvatar(
    user: User,
    size: androidx.compose.ui.unit.Dp = 40.dp,
    onClick: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    val avatarModifier = modifier
        .size(size)
        .clip(CircleShape)
        .let { if (onClick != null) it else it }
    
    if (user.avatarUrl != null) {
        // In a real app, you would use AsyncImage from Coil
        // For now, we'll use a placeholder
        Box(
            modifier = avatarModifier,
            contentAlignment = Alignment.Center
        ) {
            Surface(
                modifier = Modifier.fillMaxSize(),
                color = MaterialTheme.colorScheme.primary,
                shape = CircleShape
            ) {
                Box(contentAlignment = Alignment.Center) {
                    Text(
                        text = user.displayName.take(1).uppercase(),
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onPrimary,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
    } else {
        Surface(
            modifier = avatarModifier,
            color = MaterialTheme.colorScheme.primary,
            shape = CircleShape,
            onClick = onClick ?: {}
        ) {
            Box(contentAlignment = Alignment.Center) {
                Text(
                    text = user.displayName.take(1).uppercase(),
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onPrimary,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

@Composable
fun TagRow(
    tags: List<String>,
    modifier: Modifier = Modifier
) {
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(6.dp),
        contentPadding = PaddingValues(horizontal = 0.dp)
    ) {
        items(tags) { tag ->
            TagChip(tag = tag)
        }
    }
}

@Composable
fun TagChip(
    tag: String,
    modifier: Modifier = Modifier
) {
    val backgroundColor = getTagColor(tag)

    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp),
        color = backgroundColor
    ) {
        Text(
            text = "#$tag",
            modifier = Modifier.padding(horizontal = 10.dp, vertical = 4.dp),
            style = MaterialTheme.typography.labelSmall.copy(
                fontWeight = FontWeight.Medium,
                fontSize = 11.sp
            ),
            color = Color.White
        )
    }
}

@Composable
private fun getTagColor(tag: String): Color {
    return when {
        tag.contains("Review", ignoreCase = true) -> Color(0xFFFF8C00) // Dark orange
        tag.contains("TV", ignoreCase = true) || tag.contains("Series", ignoreCase = true) -> Color(0xFFFF6B35) // Orange red
        tag.contains("Movie", ignoreCase = true) || tag.contains("Film", ignoreCase = true) -> Color(0xFFFF9500) // Orange
        tag.contains("Character", ignoreCase = true) || tag.contains("Analysis", ignoreCase = true) -> Color(0xFFFF7F00) // Orange
        tag.contains("Supporting", ignoreCase = true) -> Color(0xFFFF8C42) // Light orange
        // Show/series specific tags
        tag.contains("TheLastOfUs", ignoreCase = true) || tag.contains("LastOfUs", ignoreCase = true) -> Color(0xFFFF6B35)
        tag.contains("Gilmore", ignoreCase = true) -> Color(0xFFFF8C00)
        tag.contains("Paris", ignoreCase = true) -> Color(0xFFFF7F00)
        // Default orange for other tags
        else -> Color(0xFFFF8C00)
    }
}

@Composable
fun ActionButton(
    icon: String,
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    TextButton(
        onClick = onClick,
        modifier = modifier,
        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp)
    ) {
        Text(
            text = icon,
            style = MaterialTheme.typography.titleMedium
        )
        Spacer(modifier = Modifier.width(4.dp))
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

@Composable
fun LoadingIndicator(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxWidth(),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator(
            modifier = Modifier.padding(16.dp)
        )
    }
}

@Composable
fun ErrorMessage(
    message: String,
    onRetry: (() -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "⚠️",
                style = MaterialTheme.typography.headlineMedium
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = message,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onErrorContainer
            )
            if (onRetry != null) {
                Spacer(modifier = Modifier.height(12.dp))
                Button(
                    onClick = onRetry,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("Retry")
                }
            }
        }
    }
}

// Utility function to format time ago
fun formatTimeAgo(timestamp: String): String {
    // In a real app, you would parse the timestamp and calculate the time difference
    // For now, we'll return a mock value based on different timestamps
    return when {
        timestamp.contains("2024-01-15") -> "2 hours ago"
        timestamp.contains("2024-01-14") -> "1 day ago"
        timestamp.contains("2024-01-13") -> "2 days ago"
        else -> "2 hours ago"
    }
}

