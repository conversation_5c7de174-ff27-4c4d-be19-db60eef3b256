package com.peliplat.data.remote

object ApiConfig {
    // Production Peliplat API endpoints (based on real website structure)
    const val PRODUCTION_BASE_URL = "https://www.peliplat.com/api"
    const val PELIPLAT_WEB_URL = "https://www.peliplat.com"
    const val PELIPLAT_IMG_URL = "https://img.peliplat.com"

    // Development/Mock endpoints for testing
    const val DEVELOPMENT_BASE_URL = "https://api.peliplat.com/v1"
    
    // Current environment - can be changed for testing
    var isProduction = true
    
    val baseUrl: String
        get() = if (isProduction) PRODUCTION_BASE_URL else DEVELOPMENT_BASE_URL
    
    // API endpoints
    object Endpoints {
        const val LOGIN = "/auth/login"
        const val REGISTER = "/auth/register"
        const val LOGOUT = "/auth/logout"
        const val REFRESH_TOKEN = "/auth/refresh"
        
        const val ARTICLES = "/articles"
        const val ARTICLE_BY_ID = "/articles/{id}"
        const val CREATE_ARTICLE = "/articles"
        const val LIKE_ARTICLE = "/articles/{id}/like"
        const val BOOKMARK_ARTICLE = "/articles/{id}/bookmark"
        
        const val DISCUSSIONS = "/discussions"
        const val DISCUSSION_BY_ID = "/discussions/{id}"
        const val CREATE_DISCUSSION = "/discussions"
        const val LIKE_DISCUSSION = "/discussions/{id}/like"
        
        const val USERS = "/users"
        const val USER_BY_ID = "/users/{id}"
        const val CURRENT_USER = "/users/me"
        const val FOLLOW_USER = "/users/{id}/follow"
        const val USER_ARTICLES = "/users/{id}/articles"
        
        const val SEARCH = "/search"
        const val SEARCH_ARTICLES = "/search/articles"
        const val SEARCH_DISCUSSIONS = "/search/discussions"
        const val SEARCH_USERS = "/search/users"
        
        const val TAGS = "/tags"
        const val POPULAR_TAGS = "/tags/popular"
        const val TAG_CONTENT = "/tags/{tag}/content"
        
        const val COMMENTS = "/comments"
        const val ARTICLE_COMMENTS = "/articles/{id}/comments"
        const val CREATE_COMMENT = "/articles/{id}/comments"
        
        const val UPLOAD_IMAGE = "/upload/image"
        const val UPLOAD_VIDEO = "/upload/video"
    }
    
    // Request timeouts
    const val CONNECT_TIMEOUT = 30_000L // 30 seconds
    const val REQUEST_TIMEOUT = 60_000L // 60 seconds
    const val SOCKET_TIMEOUT = 60_000L // 60 seconds
    
    // Pagination defaults
    const val DEFAULT_PAGE_SIZE = 20
    const val MAX_PAGE_SIZE = 100
    
    // Cache settings
    const val CACHE_MAX_AGE = 300 // 5 minutes
    const val CACHE_MAX_STALE = 3600 // 1 hour
    
    // API versioning
    const val API_VERSION = "v1"
    const val USER_AGENT = "PeliplatKMP/1.0"
    
    // Error codes
    object ErrorCodes {
        const val UNAUTHORIZED = 401
        const val FORBIDDEN = 403
        const val NOT_FOUND = 404
        const val VALIDATION_ERROR = 422
        const val RATE_LIMITED = 429
        const val SERVER_ERROR = 500
        const val NETWORK_ERROR = -1
    }
    
    // Content types
    object ContentTypes {
        const val JSON = "application/json"
        const val FORM_DATA = "multipart/form-data"
        const val URL_ENCODED = "application/x-www-form-urlencoded"
    }
}
